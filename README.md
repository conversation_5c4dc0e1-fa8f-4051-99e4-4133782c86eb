# BIT1213/BCC1213 Operating System Final Assignment

## Assignment Information
- **Course**: BIT1213/BCC1213-OPERATING SYSTEM
- **Weight**: Final Assessment (40%)
- **Mode**: Group of 2-3 students
- **Submission Date**: 4 August 2025 (MONDAY)
- **Submission Format**: Written report (Hard Copy & Soft Copy)
  - One printed copy (by group)
  - Spark (Individual)

## Course Learning Outcome (CLO)
**CLO3**: Demonstrate a simple software solution and test its performance across various operating systems (OS) (P5, PLO3)

## Assignment Objective
This practical assignment is designed to assess your understanding and application of core operating system (OS) concepts, including memory management, process/thread handling, and file system operations. You will develop a small-scale software solution that demonstrates these OS-level functionalities and test its performance on two different operating systems (e.g., Windows and Linux).

## Assignment Requirements

### 1. Program Development
- Design and implement a simple software system that demonstrates at least one OS-level functionality
- Examples: memory usage, multithreading, file I/O, or system commands
- Must be executable on both Windows and Linux
- Must produce meaningful, observable output
- Must be original work

### 2. Cross-Platform Testing
- Execute the program on at least two different operating systems (Windows and Linux)
- Record performance metrics using OS tools:
  - Execution time
  - Memory usage
  - CPU utilization
- Tools examples: `time`, `top`, Task Manager

### 3. Analysis and Comparison
- Analyze and compare the performance data between operating systems
- Include screenshots, performance charts, tables, and interpretations

### 4. Documentation and Reflection
- Submit a comprehensive report
- Include personal reflection on OS concepts demonstrated
- Present solution and findings in a short presentation

## Code Requirements
Your software must:
- Be developed using **Java, Python, or C++**
- Contain at least one operating system concept:
  - Threading
  - Memory management
  - File I/O
  - System calls
- Be executable on both Windows and Linux
- Produce meaningful, observable output
- Be your original work

## Suggested Project Ideas
Choose ONE of the following (or propose your own with instructor approval):

### 1. Simple File Organizer
- Organize files by type, date, or size
- Compare I/O efficiency across operating systems

### 2. Multi-Threaded Calculator
- Perform arithmetic using multiple threads
- Test CPU usage and thread behavior

### 3. Memory Benchmark Tool
- Allocate and manipulate data structures
- Measure memory usage patterns

### 4. Custom Shell
- Accept and execute basic commands (e.g., ls, mkdir)
- Explore system call execution

### 5. Simple Process Monitor
- Display current processes, CPU usage, and PID across OSes
- Compare process management between systems

## Report Structure (5-10 pages PDF)

### Part 1 – Program Development (Code)
- Implement a working system-level software program
- Include at least one OS concept (threading, memory tracking, file handling)
- Ensure the program runs correctly and provides output

### Part 2 – Cross-Platform Testing
- Test the program on Windows and Linux
- Use system tools to record execution time, memory, and CPU
- Document testing methodology

### Part 3 – Analysis & Report
- Compare OS behaviors using collected data
- Include screenshots, performance charts, tables, and interpretations
- Provide detailed analysis of differences

### Part 4 – Reflection and Documentation
- Reflect on what you learned
- Explain OS concept applied
- Discuss challenges encountered
- Explain how the program demonstrates OS principles

## Deliverables
Each student/group must submit:
- **Source code** (.java, .py, or .cpp)
- **Report** (PDF, 5–10 pages) containing:
  - Introduction
  - Program explanation
  - OS environments and setup
  - Test results and analysis (Part 1 to Part 4)

## Assessment Rubric (Total: 100 marks)

### 1. Clarity of Objectives (10%)
- Clear explanation of problem and objectives
- Strong understanding of OS concepts

### 2. Code Implementation (25%)
- Complete, well-structured, error-free code
- Meets all specified functionality

### 3. OS Concept Application (20%)
- Effective application of OS concepts
- Deep understanding and usage

### 4. Testing & Analysis (15%)
- Testing on 2 OS platforms
- Performance metrics and clear comparison

### 5. Reflection & Discussion (20%)
- Deep insights into learning experience
- Critical reflection on challenges and outcomes

### 6. Report Presentation (10%)
- Well-organized, professional report
- Includes screenshots, outputs, and reflection

## Project Status
- [ ] Project selection
- [ ] Development environment setup
- [ ] Code implementation
- [ ] Windows testing
- [ ] Linux testing
- [ ] Performance analysis
- [ ] Report writing
- [ ] Final submission

## Notes
- PDF files in this repository contain the original assignment documents
- Do not attempt to read PDF files programmatically
- Focus on implementation and cross-platform testing
