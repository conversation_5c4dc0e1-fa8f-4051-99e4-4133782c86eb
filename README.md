# BIT1213/BCC1213 Operating System Final Assignment

## Assignment Information
- **Course**: BIT1213/BCC1213-OPERATING SYSTEM
- **Weight**: Final Assessment (40%)
- **Mode**: Group of 2-3 students
- **Submission Date**: 4 August 2025 (MONDAY)
- **Submission Format**: Written report (Hard Copy & Soft Copy)
  - One printed copy (by group)
  - Spark (Individual)

## Course Learning Outcome (CLO)
**CLO3**: Demonstrate a simple software solution and test its performance across various operating systems (OS) (P5, PLO3)

## Assignment Objective
This practical assignment is designed to assess your understanding and application of core operating system (OS) concepts, including memory management, process/thread handling, and file system operations. You will develop a small-scale software solution that demonstrates these OS-level functionalities and test its performance on two different operating systems (e.g., Windows and Linux).

## Assignment Requirements

### 1. Program Development
- Design and implement a simple software system that demonstrates at least one OS-level functionality
- Examples: memory usage, multithreading, file I/O, or system commands
- Must be executable on both Windows and Linux
- Must produce meaningful, observable output
- Must be original work

### 2. Cross-Platform Testing
- Execute the program on at least two different operating systems (Windows and Linux)
- Record performance metrics using OS tools:
  - Execution time
  - Memory usage
  - CPU utilization
- Tools examples: `time`, `top`, Task Manager

### 3. Analysis and Comparison
- Analyze and compare the performance data between operating systems
- Include screenshots, performance charts, tables, and interpretations

### 4. Documentation and Reflection
- Submit a comprehensive report
- Include personal reflection on OS concepts demonstrated
- Present solution and findings in a short presentation

## Code Requirements
Your software must:
- Be developed using **Java, Python, or C++**
- Contain at least one operating system concept:
  - Threading
  - Memory management
  - File I/O
  - System calls
- Be executable on both Windows and Linux
- Produce meaningful, observable output
- Be your original work

## Suggested Project Ideas
Choose ONE of the following (or propose your own with instructor approval):

### 1. Simple File Organizer
- Organize files by type, date, or size
- Compare I/O efficiency across operating systems

### 2. Multi-Threaded Calculator
- Perform arithmetic using multiple threads
- Test CPU usage and thread behavior

### 3. Memory Benchmark Tool
- Allocate and manipulate data structures
- Measure memory usage patterns

### 4. Custom Shell
- Accept and execute basic commands (e.g., ls, mkdir)
- Explore system call execution

### 5. Simple Process Monitor
- Display current processes, CPU usage, and PID across OSes
- Compare process management between systems

## Report Structure (5-10 pages PDF)

### Part 1 – Program Development (Code)
- Implement a working system-level software program
- Include at least one OS concept (threading, memory tracking, file handling)
- Ensure the program runs correctly and provides output

### Part 2 – Cross-Platform Testing
- Test the program on Windows and Linux
- Use system tools to record execution time, memory, and CPU
- Document testing methodology

### Part 3 – Analysis & Report
- Compare OS behaviors using collected data
- Include screenshots, performance charts, tables, and interpretations
- Provide detailed analysis of differences

### Part 4 – Reflection and Documentation
- Reflect on what you learned
- Explain OS concept applied
- Discuss challenges encountered
- Explain how the program demonstrates OS principles

## Deliverables
Each student/group must submit:
- **Source code** (.java, .py, or .cpp)
- **Report** (PDF, 5–10 pages) containing:
  - Introduction
  - Program explanation
  - OS environments and setup
  - Test results and analysis (Part 1 to Part 4)

## Assessment Rubric (Total: 100 marks)

### 1. Clarity of Objectives (10%)
| Grade | Score | Criteria |
|-------|-------|----------|
| **Excellent (5)** | 5 | Clearly explains the problem and objectives; demonstrates strong understanding of OS concept used |
| **Good (4)** | 4 | Mostly clear explanation of the goal; good understanding of the OS concept |
| **Satisfactory (3)** | 3 | General explanation with some basic understanding of OS concept |
| **Fair (2)** | 2 | Limited explanation or unclear objectives; weak understanding |
| **Needs Improvement (0-1)** | 0-1 | Missing or extremely weak explanation of project purpose and OS concept |

### 2. Code Implementation (25%)
| Grade | Score | Criteria |
|-------|-------|----------|
| **Excellent (5)** | 5 | Code is complete, well-structured, well-documented, and meets all intended functionality |
| **Good (4)** | 4 | Code mostly works well; minor issues or incomplete elements |
| **Satisfactory (3)** | 3 | Basic functionality is present; may lack polish or complexity |
| **Fair (2)** | 2 | Partially functioning code; several issues with execution |
| **Needs Improvement (0-1)** | 0-1 | Code does not run or is incomplete with major execution issues |

### 3. OS Concept Application (20%)
| Grade | Score | Criteria |
|-------|-------|----------|
| **Excellent (5)** | 5 | Effectively applies OS concept(s) such as memory or file handling; depth in use |
| **Good (4)** | 4 | Applies at least one OS concept with general accuracy and depth |
| **Satisfactory (3)** | 3 | Applies OS concept but lacks clarity or depth in use |
| **Fair (2)** | 2 | Weak attempt to apply OS concept; not clearly |
| **Needs Improvement (0-1)** | 0-1 | No clear application of OS concept or incorrect |

### 4. Testing & Analysis (15%)
| Grade | Score | Criteria |
|-------|-------|----------|
| **Excellent (5)** | 5 | Program tested on 2 OS platforms; analysis includes performance metrics and clear comparison |
| **Good (4)** | 4 | Tested on both platforms; general comparison provided |
| **Satisfactory (3)** | 3 | Basic testing done; minimal comparison or missing key metrics |
| **Fair (2)** | 2 | Limited testing or unclear comparison between platforms |
| **Needs Improvement (0-1)** | 0-1 | No testing on different OS or lacks comparison |

### 5. Reflection & Discussion (20%)
| Grade | Score | Criteria |
|-------|-------|----------|
| **Excellent (5)** | 5 | Provides deep insights into learning experience, OS behavior observed, and critical reflection on challenges and outcomes |
| **Good (4)** | 4 | Good reflection on learning and OS behavior; some critical points are addressed |
| **Satisfactory (3)** | 3 | Basic reflection included with limited discussion of challenges or learning |
| **Fair (2)** | 2 | Minimal reflection; unclear what was learned or experienced |
| **Needs Improvement (0-1)** | 0-1 | No meaningful reflection; lacks connection to the work done |

### 6. Report Presentation (10%)
| Grade | Score | Criteria |
|-------|-------|----------|
| **Excellent (5)** | 5 | Report is well-organized, professional, includes screenshots, outputs, and reflection |
| **Good (4)** | 4 | Well-written with few formatting issues; most required elements are included |
| **Satisfactory (3)** | 3 | Readable report with minor organization or content gaps |
| **Fair (2)** | 2 | Report is hard to follow; missing elements or poor formatting |
| **Needs Improvement (0-1)** | 0-1 | Poorly presented, unstructured, and lacks required documentation |

## Selected Project: Multi-Threaded Calculator

### Project Choice
We have selected the **Multi-Threaded Calculator** project because it:
- Clearly demonstrates threading concepts (core OS concept)
- Provides measurable performance metrics
- Has excellent cross-platform compatibility
- Allows for comprehensive performance analysis

### Key Features
- Large number arithmetic operations (factorial, prime generation)
- Thread pool management and task distribution
- Performance monitoring (CPU, memory, execution time)
- Single-threaded vs multi-threaded comparison
- Cross-platform testing and analysis

### Technology Stack
- **Language**: Python 3.8+
- **Threading**: concurrent.futures.ThreadPoolExecutor
- **Monitoring**: psutil, time, threading modules
- **Platforms**: Windows 10/11, Linux (Ubuntu/CentOS)

## Project Status
- [x] Project selection (Multi-Threaded Calculator)
- [x] Project planning and design
- [x] Development environment setup
- [x] Core functionality implementation
- [x] Performance monitoring integration
- [x] Windows platform testing
- [x] Linux platform testing (scripts prepared)
- [x] Performance analysis and comparison
- [x] Report writing
- [x] Final submission preparation

## Project Completion Summary

### ✅ Successfully Completed Components

1. **Multi-Threaded Calculator Implementation**
   - Complete mathematical operations module
   - Threading manager with performance comparison
   - Comprehensive performance monitoring system
   - Cross-platform compatibility design

2. **Performance Testing and Analysis**
   - Windows platform comprehensive benchmarking
   - Performance visualization and charts
   - Detailed analysis reports
   - Cross-platform comparison framework

3. **Documentation and Reporting**
   - Complete technical report (Final_Technical_Report.md)
   - Performance analysis with visualizations
   - Cross-platform testing scripts
   - Comprehensive code documentation

### 📊 Key Results Achieved

**Prime Search Benchmark (1-50,000):**
- Single-threaded: 0.0556s
- Best multi-threaded: 0.0421s (8 threads, 1.32x speedup)
- Found 5,133 prime numbers

**CPU Intensive Benchmark (500,000 iterations):**
- Single-threaded: 0.1429s
- Best multi-threaded: 0.1257s (8 threads, 1.14x speedup)

**Memory Intensive Benchmark (30 MB):**
- Single-threaded: 0.3733s
- Multi-threaded showed performance degradation due to memory contention

### 🔍 OS Concepts Demonstrated

1. **Threading and Concurrency**: ThreadPoolExecutor implementation
2. **CPU Scheduling**: Thread distribution across cores
3. **Memory Management**: Memory allocation patterns analysis
4. **System Resources**: Resource contention and optimization

### 📁 Generated Files and Artifacts

**Source Code:**
- `multithreaded_calculator/` - Complete application package
- `demo_performance.py` - Performance demonstration script
- `windows_benchmark.py` - Windows testing script
- `linux_benchmark.py` - Linux testing script
- `cross_platform_analysis.py` - Platform comparison tool
- `performance_analysis.py` - Detailed analysis generator

**Results and Reports:**
- `Final_Technical_Report.md` - Complete technical report
- `performance_charts/` - Performance visualizations
- `multithreaded_calculator/results/` - Benchmark data
- Various analysis reports with timestamps

**Testing and Validation:**
- Unit tests for all core components
- Integration tests for threading functionality
- Performance validation scripts
- Cross-platform compatibility verification

## Notes
- PDF files in this repository contain the original assignment documents
- Do not attempt to read PDF files programmatically
- Focus on implementation and cross-platform testing
