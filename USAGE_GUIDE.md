# Multi-Threaded Calculator Usage Guide

## Quick Start

### 1. Environment Setup
```bash
# Check Python version (3.8+ required)
python --version

# Install dependencies
pip install -r requirements.txt

# Verify setup
python setup_environment.py
```

### 2. Run Basic Demo
```bash
# Quick performance demonstration
python demo_performance.py
```

### 3. Run Comprehensive Benchmarks
```bash
# Windows platform testing
python windows_benchmark.py

# Linux platform testing (on Linux systems)
python linux_benchmark.py
```

### 4. Generate Analysis Reports
```bash
# Create performance analysis and visualizations
python performance_analysis.py

# Cross-platform comparison (requires both Windows and Linux results)
python cross_platform_analysis.py
```

## Detailed Usage

### Interactive Mode
```bash
python multithreaded_calculator/main.py --mode interactive
```

Available operations:
1. Prime Search - Find prime numbers in a range
2. CPU Intensive Task - Mathematical computations
3. Memory Intensive Task - Large data structure operations
4. Run All Benchmarks - Complete performance suite
5. Exit

### Command Line Options
```bash
# Show system information only
python multithreaded_calculator/main.py --mode info

# Run benchmark mode with custom parameters
python multithreaded_calculator/main.py --mode benchmark --prime-range 1 100000 --cpu-iterations 1000000 --memory-size 50
```

### Running Unit Tests
```bash
# Run all tests
python -m pytest multithreaded_calculator/tests/ -v

# Run specific test file
python -m pytest multithreaded_calculator/tests/test_operations.py -v

# Run with coverage
python -m pytest multithreaded_calculator/tests/ --cov=multithreaded_calculator --cov-report=html
```

## Understanding the Results

### Performance Metrics

**Execution Time**: Wall-clock time for task completion
**Speedup**: Ratio of single-threaded time to multi-threaded time
**Efficiency**: Speedup divided by number of threads (as percentage)

### Interpreting Speedup
- **> 1.0**: Multi-threading provides benefit
- **= 1.0**: No benefit from multi-threading
- **< 1.0**: Multi-threading hurts performance (overhead)

### Efficiency Guidelines
- **> 80%**: Excellent threading efficiency
- **50-80%**: Good threading efficiency
- **< 50%**: Poor threading efficiency (consider fewer threads)

## File Structure

```
Final_Assignment_vfinal/
├── README.md                           # Project overview
├── USAGE_GUIDE.md                     # This file
├── Final_Technical_Report.md          # Complete technical report
├── requirements.txt                   # Python dependencies
├── setup_environment.py              # Environment setup script
├── demo_performance.py               # Quick demonstration
├── windows_benchmark.py              # Windows testing
├── linux_benchmark.py                # Linux testing
├── cross_platform_analysis.py        # Platform comparison
├── performance_analysis.py           # Detailed analysis
├── multithreaded_calculator/          # Main application package
│   ├── __init__.py
│   ├── main.py                       # Application entry point
│   ├── calculator/                   # Core modules
│   │   ├── operations.py            # Mathematical operations
│   │   ├── threading_manager.py     # Thread management
│   │   └── performance_monitor.py   # Performance monitoring
│   ├── utils/                       # Utility functions
│   ├── tests/                       # Unit tests
│   └── results/                     # Performance data
│       ├── windows_results/
│       └── linux_results/
└── performance_charts/               # Generated visualizations
```

## Performance Data Files

### Benchmark Results
- **JSON Format**: Complete benchmark data with system information
- **Location**: `multithreaded_calculator/results/[platform]_results/`
- **Naming**: `[platform]_benchmark_YYYYMMDD_HHMMSS.json`

### Analysis Reports
- **Text Format**: Human-readable analysis reports
- **Location**: Project root directory
- **Naming**: `performance_analysis_report_YYYYMMDD_HHMMSS.txt`

### Visualizations
- **PNG Format**: Performance charts and graphs
- **Location**: `performance_charts/` directory
- **Types**: Execution time, speedup analysis, efficiency analysis, summary tables

## Troubleshooting

### Common Issues

**Import Errors**:
```bash
# Ensure you're in the correct directory
cd Final_Assignment_vfinal

# Check Python path
python -c "import sys; print(sys.path)"
```

**Missing Dependencies**:
```bash
# Reinstall requirements
pip install -r requirements.txt --force-reinstall
```

**Permission Errors**:
```bash
# On Windows, run as Administrator if needed
# On Linux, check file permissions
chmod +x *.py
```

**Performance Monitoring Issues**:
```bash
# Install psutil if missing
pip install psutil

# Check system permissions for monitoring
python -c "import psutil; print(psutil.cpu_percent())"
```

### Platform-Specific Notes

**Windows**:
- Use PowerShell or Command Prompt
- May require Administrator privileges for detailed monitoring
- Task Manager can be used for additional performance verification

**Linux**:
- Ensure Python 3.8+ is installed
- May need to install additional packages: `sudo apt-get install python3-dev`
- Use `top` or `htop` for additional monitoring during execution

## Advanced Usage

### Custom Benchmarks

Create custom benchmark functions in `operations.py`:

```python
@staticmethod
def custom_operation(param1, param2):
    # Your custom computation here
    return result
```

Add to threading manager in `threading_manager.py`:

```python
elif operation == "custom":
    result = MathOperations.custom_operation(*args, **kwargs)
```

### Extended Monitoring

Modify `performance_monitor.py` to add custom metrics:

```python
# Add custom data points in _monitor_loop
data_point['custom_metric'] = get_custom_metric()
```

### Cross-Platform Testing

For comprehensive cross-platform analysis:

1. Run `windows_benchmark.py` on Windows system
2. Copy project to Linux system
3. Run `linux_benchmark.py` on Linux system
4. Copy Linux results back to Windows
5. Run `cross_platform_analysis.py` for comparison

## Educational Value

### OS Concepts Demonstrated

1. **Threading**: Thread creation, management, synchronization
2. **CPU Scheduling**: Load balancing, core utilization
3. **Memory Management**: Allocation patterns, garbage collection
4. **Resource Management**: Contention, optimization

### Learning Objectives

- Understand practical threading implementation
- Analyze performance characteristics of parallel processing
- Observe operating system behavior in multi-threaded applications
- Compare performance across different platforms and configurations

### Extension Opportunities

- Implement in other languages (C++, Java) for comparison
- Add GPU acceleration using CUDA or OpenCL
- Explore different threading models (async/await, multiprocessing)
- Investigate NUMA effects on multi-core systems

## Support and Documentation

### Additional Resources

- **Technical Report**: `Final_Technical_Report.md` - Complete project documentation
- **Source Code**: Fully commented and documented
- **Unit Tests**: Examples of proper testing practices
- **Performance Data**: Real benchmark results for analysis

### Contact and Feedback

This project was developed as part of BIT1213/BCC1213 Operating System course. For questions or improvements, refer to the course materials and instructor guidance.

---

**Happy Computing! 🚀**
