"""
Unit tests for mathematical operations module.
"""

import unittest
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from multithreaded_calculator.calculator.operations import MathOperations, TaskDistributor


class TestMathOperations(unittest.TestCase):
    """Test cases for MathOperations class."""
    
    def test_large_factorial(self):
        """Test factorial calculation."""
        self.assertEqual(MathOperations.large_factorial(0), 1)
        self.assertEqual(MathOperations.large_factorial(1), 1)
        self.assertEqual(MathOperations.large_factorial(5), 120)
        self.assertEqual(MathOperations.large_factorial(10), 3628800)
    
    def test_prime_check(self):
        """Test prime number checking."""
        self.assertFalse(MathOperations.prime_check(1))
        self.assertTrue(MathOperations.prime_check(2))
        self.assertTrue(MathOperations.prime_check(3))
        self.assertFalse(MathOperations.prime_check(4))
        self.assertTrue(MathOperations.prime_check(17))
        self.assertFalse(MathOperations.prime_check(100))
    
    def test_find_primes_in_range(self):
        """Test prime finding in range."""
        primes_1_to_10 = MathOperations.find_primes_in_range(1, 10)
        expected = [2, 3, 5, 7]
        self.assertEqual(primes_1_to_10, expected)
        
        primes_10_to_20 = MathOperations.find_primes_in_range(10, 20)
        expected = [11, 13, 17, 19]
        self.assertEqual(primes_10_to_20, expected)
    
    def test_fibonacci_sequence(self):
        """Test Fibonacci sequence generation."""
        self.assertEqual(MathOperations.fibonacci_sequence(0), [])
        self.assertEqual(MathOperations.fibonacci_sequence(1), [0])
        self.assertEqual(MathOperations.fibonacci_sequence(2), [0, 1])
        self.assertEqual(MathOperations.fibonacci_sequence(8), [0, 1, 1, 2, 3, 5, 8, 13])
    
    def test_cpu_intensive_task(self):
        """Test CPU intensive task."""
        result = MathOperations.cpu_intensive_task(1000)
        self.assertIsInstance(result, float)
        # Result can be positive or negative due to sin/cos calculations
        self.assertNotEqual(result, 0)
    
    def test_memory_intensive_task(self):
        """Test memory intensive task."""
        result = MathOperations.memory_intensive_task(1)  # 1 MB
        self.assertIsInstance(result, list)
        self.assertGreater(len(result), 0)
    
    def test_create_random_matrix(self):
        """Test random matrix creation."""
        matrix = MathOperations.create_random_matrix(3, seed=42)
        self.assertEqual(matrix.shape, (3, 3))
        
        # Test reproducibility with same seed
        matrix2 = MathOperations.create_random_matrix(3, seed=42)
        self.assertTrue((matrix == matrix2).all())


class TestTaskDistributor(unittest.TestCase):
    """Test cases for TaskDistributor class."""
    
    def test_split_range(self):
        """Test range splitting."""
        chunks = TaskDistributor.split_range(1, 100, 4)
        self.assertEqual(len(chunks), 4)
        
        # Check that all numbers are covered
        all_numbers = set()
        for start, end in chunks:
            all_numbers.update(range(start, end + 1))
        
        expected_numbers = set(range(1, 101))
        self.assertEqual(all_numbers, expected_numbers)
    
    def test_split_list(self):
        """Test list splitting."""
        data = list(range(100))
        chunks = TaskDistributor.split_list(data, 4)
        
        self.assertEqual(len(chunks), 4)
        
        # Check that all elements are covered
        all_elements = []
        for chunk in chunks:
            all_elements.extend(chunk)
        
        self.assertEqual(sorted(all_elements), data)
    
    def test_split_range_edge_cases(self):
        """Test edge cases for range splitting."""
        # More chunks than range
        chunks = TaskDistributor.split_range(1, 3, 5)
        self.assertEqual(len(chunks), 5)
        
        # Single element range
        chunks = TaskDistributor.split_range(5, 5, 2)
        self.assertEqual(len(chunks), 2)


if __name__ == '__main__':
    unittest.main()
