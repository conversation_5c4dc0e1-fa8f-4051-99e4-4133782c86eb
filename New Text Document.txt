BIT1213/BCC1213-OPERATING SYSTEM
Final Assessment (40%)
Instruction:
1.
This assignment is group work and contributes 40% of the overall assessment.
2.
Mode: Group of 2-3 students
3.
Submission in the form of written report (Hard Copy & Soft Copy)
a.
One printed copy (by group)
b<PERSON>
<PERSON> (Individual)
4.
Date of Submission: 4 August 2025 (MONDAY)
Objective of the Assignment
This practical assignment is designed to assess your understanding and application of core operating system (OS) concepts, including memory management, process/thread handling, and file system operations. You will develop a small-scale software solution that demonstrates these OS-level functionalities and test its performance on two different operating systems (e.g., Windows and Linux).
CLO3: Demonstrate a simple software solution and test its performance across various operating systems (OS) (P5, PLO3)
Assignment Overview
You are required to complete the following:
1. Design and implement a simple software system that demonstrates at least one OS-level functionality (e.g., memory usage, multithreading, file I/O, or system commands).
2. Execute the program on at least two different operating systems (e.g., Windows and Linux).
3. Record performance metrics such as execution time, memory usage, and CPU utilization using OS tools (e.g., time, top, Task Manager).
4. Analyze and compare the performance data between the operating systems.
5. Submit a report that includes system explanation, testing outcomes, analysis, and a personal reflection on the OS concepts demonstrated.
6. Present your solution and findings in a short presentation.
Suggested Project Titles / Software Ideas
Choose ONE of the following software ideas, or propose your own (subject to instructor approval):
1. Simple File Organizer
– Organize files by type, date, or size and compare I/O efficiency.
2. Multi-Threaded Calculator
– Perform arithmetic using multiple threads; test CPU usage and thread behavior.
3. Memory Benchmark Tool
– Allocate and manipulate data structures; measure memory usage.
4. Custom Shell
– Accept and execute basic commands (e.g., ls, mkdir); explore system call execution.
5. Simple Process Monitor – Display current processes, CPU usage, and PID across OSes.
Assignment Structure
Your report must contain the following components:
Part 1 – Program Development (Code) : • Implement a working system-level software program. • Include at least one OS concept (e.g., threading, memory tracking, file handling). • Ensure the program runs correctly and provides output. Part 2 – Cross-Platform Testing : • Test the program on Windows and Linux. • Use system tools (e.g., top, time, Task Manager) to record execution time, memory, and CPU. Part 3 – Analysis & Report : • Compare OS behaviors using the collected data. • Include screenshots, performance charts, tables, and interpretations. Part 4 – Reflection and Documentation: • Reflect on what you learned. • Explain OS concept applied, challenges encountered, and how the program demonstrates OS principles.
Deliverables and Submission Format
Each student/group must submit: • Source code (.java, .py, or .cpp) • Report (PDF, 5–10 pages) containing: - Introduction - Program explanation - OS environments and setup - Test results and analysis (Part 1 to Part 4)
Code Requirements
Your software must: • Be developed using Java, Python, or C++. • Contain at least one operating system concept (threading, memory, file I/O, or system call). • Be executable on both Windows and Linux. • Produce meaningful, observable output. • Be your original work.
