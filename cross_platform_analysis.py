#!/usr/bin/env python3
"""
Cross-Platform Performance Analysis

This script analyzes and compares performance results between
Windows and Linux platforms, generating comprehensive reports
and visualizations.
"""

import os
import json
import glob
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Any


class CrossPlatformAnalyzer:
    """Analyzes performance data across different platforms."""
    
    def __init__(self):
        self.windows_results = []
        self.linux_results = []
        self.comparison_data = {}
    
    def load_results(self):
        """Load benchmark results from both platforms."""
        # Load Windows results
        windows_dir = os.path.join("multithreaded_calculator", "results", "windows_results")
        if os.path.exists(windows_dir):
            for file_path in glob.glob(os.path.join(windows_dir, "*.json")):
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        self.windows_results.append(data)
                        print(f"✅ Loaded Windows result: {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"❌ Error loading {file_path}: {e}")
        
        # Load Linux results
        linux_dir = os.path.join("multithreaded_calculator", "results", "linux_results")
        if os.path.exists(linux_dir):
            for file_path in glob.glob(os.path.join(linux_dir, "*.json")):
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                        self.linux_results.append(data)
                        print(f"✅ Loaded Linux result: {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"❌ Error loading {file_path}: {e}")
    
    def extract_performance_data(self, results: List[Dict], platform: str) -> Dict[str, Any]:
        """Extract performance metrics from results."""
        if not results:
            return {}
        
        # Use the most recent result
        latest_result = max(results, key=lambda x: x.get('timestamp', ''))
        
        performance_data = {
            'platform': platform,
            'timestamp': latest_result.get('timestamp'),
            'system_info': latest_result.get('system_info', {}),
            'benchmarks': {}
        }
        
        benchmarks = latest_result.get('benchmarks', {})
        
        for benchmark_name, benchmark_data in benchmarks.items():
            # Extract single-threaded performance
            single_threaded = benchmark_data.get('single_threaded', [])
            if single_threaded:
                single_time = single_threaded[0].get('execution_time', 0)
            else:
                single_time = 0
            
            # Extract multi-threaded performance
            multi_threaded = benchmark_data.get('multi_threaded', [])
            thread_performance = {}
            
            for result in multi_threaded:
                thread_count = result.get('thread_count', 0)
                execution_time = result.get('execution_time', 0)
                speedup = single_time / execution_time if execution_time > 0 else 0
                efficiency = speedup / thread_count * 100 if thread_count > 0 else 0
                
                thread_performance[thread_count] = {
                    'execution_time': execution_time,
                    'speedup': speedup,
                    'efficiency': efficiency
                }
            
            performance_data['benchmarks'][benchmark_name] = {
                'single_threaded_time': single_time,
                'multi_threaded': thread_performance
            }
        
        return performance_data
    
    def generate_comparison_report(self) -> str:
        """Generate a comprehensive comparison report."""
        report = []
        report.append("=" * 80)
        report.append("CROSS-PLATFORM PERFORMANCE ANALYSIS REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Extract performance data
        windows_data = self.extract_performance_data(self.windows_results, "Windows")
        linux_data = self.extract_performance_data(self.linux_results, "Linux")
        
        if not windows_data and not linux_data:
            report.append("❌ No performance data available for analysis")
            return "\n".join(report)
        
        # System Information Comparison
        report.append("💻 SYSTEM INFORMATION COMPARISON")
        report.append("-" * 50)
        
        if windows_data:
            win_sys = windows_data.get('system_info', {})
            win_cpu = win_sys.get('cpu_info', {})
            win_mem = win_sys.get('memory_info', {})
            
            report.append("Windows System:")
            report.append(f"  Physical Cores: {win_cpu.get('physical_cores', 'N/A')}")
            report.append(f"  Logical Cores: {win_cpu.get('logical_cores', 'N/A')}")
            report.append(f"  Total Memory: {win_mem.get('total_gb', 'N/A'):.2f} GB")
        
        if linux_data:
            linux_sys = linux_data.get('system_info', {})
            linux_cpu = linux_sys.get('cpu_info', {})
            linux_mem = linux_sys.get('memory_info', {})
            
            report.append("Linux System:")
            report.append(f"  Physical Cores: {linux_cpu.get('physical_cores', 'N/A')}")
            report.append(f"  Logical Cores: {linux_cpu.get('logical_cores', 'N/A')}")
            report.append(f"  Total Memory: {linux_mem.get('total_gb', 'N/A'):.2f} GB")
        
        report.append("")
        
        # Benchmark Comparisons
        if windows_data and linux_data:
            common_benchmarks = set(windows_data.get('benchmarks', {}).keys()) & \
                              set(linux_data.get('benchmarks', {}).keys())
            
            for benchmark in common_benchmarks:
                report.append(f"🔍 {benchmark.upper().replace('_', ' ')} COMPARISON")
                report.append("-" * 50)
                
                win_bench = windows_data['benchmarks'][benchmark]
                linux_bench = linux_data['benchmarks'][benchmark]
                
                # Single-threaded comparison
                win_single = win_bench['single_threaded_time']
                linux_single = linux_bench['single_threaded_time']
                
                if win_single > 0 and linux_single > 0:
                    ratio = win_single / linux_single
                    faster_platform = "Linux" if ratio > 1 else "Windows"
                    performance_diff = abs(ratio - 1) * 100
                    
                    report.append(f"Single-threaded Performance:")
                    report.append(f"  Windows: {win_single:.4f}s")
                    report.append(f"  Linux:   {linux_single:.4f}s")
                    report.append(f"  {faster_platform} is {performance_diff:.1f}% faster")
                
                # Multi-threaded comparison
                report.append(f"Multi-threaded Performance:")
                
                common_threads = set(win_bench['multi_threaded'].keys()) & \
                               set(linux_bench['multi_threaded'].keys())
                
                for thread_count in sorted(common_threads):
                    win_mt = win_bench['multi_threaded'][thread_count]
                    linux_mt = linux_bench['multi_threaded'][thread_count]
                    
                    report.append(f"  {thread_count} threads:")
                    report.append(f"    Windows: {win_mt['execution_time']:.4f}s "
                                f"(speedup: {win_mt['speedup']:.2f}x)")
                    report.append(f"    Linux:   {linux_mt['execution_time']:.4f}s "
                                f"(speedup: {linux_mt['speedup']:.2f}x)")
                
                report.append("")
        
        # Key Insights
        report.append("🔍 KEY INSIGHTS")
        report.append("-" * 50)
        
        if windows_data and linux_data:
            report.append("Threading Performance:")
            report.append("- Both platforms show threading benefits for parallelizable tasks")
            report.append("- Performance differences may be due to:")
            report.append("  * Different thread scheduling algorithms")
            report.append("  * Hardware architecture differences")
            report.append("  * Operating system overhead variations")
            report.append("  * Memory management implementations")
        else:
            report.append("- Limited data available for comprehensive comparison")
            report.append("- Run benchmarks on both platforms for full analysis")
        
        report.append("")
        report.append("Operating System Concepts Demonstrated:")
        report.append("✅ Threading: Thread creation, management, and synchronization")
        report.append("✅ CPU Scheduling: Thread scheduling across multiple cores")
        report.append("✅ Memory Management: Memory allocation and usage patterns")
        report.append("✅ System Resources: Resource contention and optimization")
        
        return "\n".join(report)
    
    def create_performance_charts(self, output_dir: str = "analysis_charts"):
        """Create performance comparison charts."""
        os.makedirs(output_dir, exist_ok=True)
        
        windows_data = self.extract_performance_data(self.windows_results, "Windows")
        linux_data = self.extract_performance_data(self.linux_results, "Linux")
        
        if not windows_data or not linux_data:
            print("⚠️  Insufficient data for chart generation")
            return []
        
        created_charts = []
        
        # Performance comparison chart
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Cross-Platform Performance Comparison', fontsize=16)
        
        benchmarks = ['prime_search', 'cpu_intensive']
        
        for i, benchmark in enumerate(benchmarks):
            if benchmark in windows_data.get('benchmarks', {}) and \
               benchmark in linux_data.get('benchmarks', {}):
                
                win_bench = windows_data['benchmarks'][benchmark]
                linux_bench = linux_data['benchmarks'][benchmark]
                
                # Execution time comparison
                ax1 = axes[i, 0]
                thread_counts = sorted(win_bench['multi_threaded'].keys())
                
                win_times = [win_bench['multi_threaded'][tc]['execution_time'] for tc in thread_counts]
                linux_times = [linux_bench['multi_threaded'][tc]['execution_time'] for tc in thread_counts]
                
                x = range(len(thread_counts))
                width = 0.35
                
                ax1.bar([i - width/2 for i in x], win_times, width, label='Windows', alpha=0.8)
                ax1.bar([i + width/2 for i in x], linux_times, width, label='Linux', alpha=0.8)
                
                ax1.set_xlabel('Thread Count')
                ax1.set_ylabel('Execution Time (s)')
                ax1.set_title(f'{benchmark.replace("_", " ").title()} - Execution Time')
                ax1.set_xticks(x)
                ax1.set_xticklabels(thread_counts)
                ax1.legend()
                ax1.grid(True, alpha=0.3)
                
                # Speedup comparison
                ax2 = axes[i, 1]
                
                win_speedups = [win_bench['multi_threaded'][tc]['speedup'] for tc in thread_counts]
                linux_speedups = [linux_bench['multi_threaded'][tc]['speedup'] for tc in thread_counts]
                
                ax2.plot(thread_counts, win_speedups, 'o-', label='Windows', linewidth=2, markersize=6)
                ax2.plot(thread_counts, linux_speedups, 's-', label='Linux', linewidth=2, markersize=6)
                ax2.plot(thread_counts, thread_counts, '--', label='Ideal', alpha=0.5)
                
                ax2.set_xlabel('Thread Count')
                ax2.set_ylabel('Speedup')
                ax2.set_title(f'{benchmark.replace("_", " ").title()} - Speedup')
                ax2.legend()
                ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        chart_file = os.path.join(output_dir, 'cross_platform_comparison.png')
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        created_charts.append(chart_file)
        
        return created_charts
    
    def analyze(self):
        """Run complete cross-platform analysis."""
        print("🔍 CROSS-PLATFORM PERFORMANCE ANALYSIS")
        print("=" * 60)
        
        # Load results
        self.load_results()
        
        if not self.windows_results and not self.linux_results:
            print("❌ No benchmark results found!")
            print("Please run benchmarks on both platforms first:")
            print("  Windows: python windows_benchmark.py")
            print("  Linux:   python linux_benchmark.py")
            return
        
        # Generate report
        report = self.generate_comparison_report()
        
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"cross_platform_analysis_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📊 Analysis report saved to: {report_file}")
        
        # Create charts if data is available
        try:
            charts = self.create_performance_charts()
            if charts:
                print(f"📈 Performance charts created: {len(charts)} files")
                for chart in charts:
                    print(f"  - {chart}")
        except Exception as e:
            print(f"⚠️  Could not create charts: {e}")
        
        # Display report
        print("\n" + report)


def main():
    """Main function."""
    analyzer = CrossPlatformAnalyzer()
    analyzer.analyze()


if __name__ == "__main__":
    main()
