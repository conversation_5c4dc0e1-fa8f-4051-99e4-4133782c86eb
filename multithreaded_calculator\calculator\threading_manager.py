"""
Threading Manager Module

This module manages thread execution and provides comparison between
single-threaded and multi-threaded execution of mathematical operations.
"""

import time
import threading
from typing import List, Dict, Any, Callable, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from .operations import MathOperations, TaskDistributor


class ThreadingManager:
    """Manages thread execution and performance comparison."""
    
    def __init__(self, max_workers: int = 4):
        """
        Initialize the threading manager.
        
        Args:
            max_workers: Maximum number of worker threads
        """
        self.max_workers = max_workers
        self.execution_results = {}
        self.performance_data = {}
    
    def execute_single_threaded(self, operation: str, *args, **kwargs) -> Dict[str, Any]:
        """
        Execute an operation in single-threaded mode.
        
        Args:
            operation: Name of the operation to execute
            *args: Arguments for the operation
            **kwargs: Keyword arguments for the operation
            
        Returns:
            Dictionary containing result and performance metrics
        """
        start_time = time.time()
        start_cpu_time = time.process_time()
        
        # Execute the operation
        if operation == "factorial":
            result = MathOperations.large_factorial(*args, **kwargs)
        elif operation == "prime_search":
            start_range, end_range = args
            result = MathOperations.find_primes_in_range(start_range, end_range)
        elif operation == "fibonacci":
            result = MathOperations.fibonacci_sequence(*args, **kwargs)
        elif operation == "cpu_intensive":
            result = MathOperations.cpu_intensive_task(*args, **kwargs)
        elif operation == "memory_intensive":
            result = MathOperations.memory_intensive_task(*args, **kwargs)
        else:
            raise ValueError(f"Unknown operation: {operation}")
        
        end_time = time.time()
        end_cpu_time = time.process_time()
        
        execution_data = {
            "result": result,
            "execution_time": end_time - start_time,
            "cpu_time": end_cpu_time - start_cpu_time,
            "thread_count": 1,
            "operation": operation,
            "args": args,
            "kwargs": kwargs
        }
        
        return execution_data
    
    def execute_multi_threaded(self, operation: str, thread_count: int, 
                             *args, **kwargs) -> Dict[str, Any]:
        """
        Execute an operation in multi-threaded mode.
        
        Args:
            operation: Name of the operation to execute
            thread_count: Number of threads to use
            *args: Arguments for the operation
            **kwargs: Keyword arguments for the operation
            
        Returns:
            Dictionary containing result and performance metrics
        """
        start_time = time.time()
        start_cpu_time = time.process_time()
        
        # Execute the operation with multiple threads
        if operation == "prime_search":
            result = self._multi_threaded_prime_search(thread_count, *args)
        elif operation == "cpu_intensive":
            result = self._multi_threaded_cpu_intensive(thread_count, *args, **kwargs)
        elif operation == "memory_intensive":
            result = self._multi_threaded_memory_intensive(thread_count, *args, **kwargs)
        elif operation == "factorial":
            # For factorial, we'll use a different approach
            result = self._multi_threaded_factorial(thread_count, *args, **kwargs)
        else:
            raise ValueError(f"Operation {operation} not supported in multi-threaded mode")
        
        end_time = time.time()
        end_cpu_time = time.process_time()
        
        execution_data = {
            "result": result,
            "execution_time": end_time - start_time,
            "cpu_time": end_cpu_time - start_cpu_time,
            "thread_count": thread_count,
            "operation": operation,
            "args": args,
            "kwargs": kwargs
        }
        
        return execution_data
    
    def _multi_threaded_prime_search(self, thread_count: int, start: int, end: int) -> List[int]:
        """Execute prime search using multiple threads."""
        # Split the range into chunks
        chunks = TaskDistributor.split_range(start, end, thread_count)
        all_primes = []
        
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            # Submit tasks to threads
            future_to_chunk = {
                executor.submit(MathOperations.find_primes_in_range, chunk_start, chunk_end): 
                (chunk_start, chunk_end)
                for chunk_start, chunk_end in chunks
            }
            
            # Collect results
            for future in as_completed(future_to_chunk):
                chunk_primes = future.result()
                all_primes.extend(chunk_primes)
        
        # Sort the results since they may come back out of order
        all_primes.sort()
        return all_primes
    
    def _multi_threaded_cpu_intensive(self, thread_count: int, 
                                    total_iterations: int = 1000000) -> float:
        """Execute CPU-intensive task using multiple threads."""
        iterations_per_thread = total_iterations // thread_count
        results = []
        
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            # Submit tasks to threads
            futures = [
                executor.submit(MathOperations.cpu_intensive_task, iterations_per_thread)
                for _ in range(thread_count)
            ]
            
            # Collect results
            for future in as_completed(futures):
                results.append(future.result())
        
        return sum(results)
    
    def _multi_threaded_memory_intensive(self, thread_count: int, 
                                       total_size_mb: int = 10) -> List[List[List[int]]]:
        """Execute memory-intensive task using multiple threads."""
        size_per_thread = max(1, total_size_mb // thread_count)
        results = []
        
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            # Submit tasks to threads
            futures = [
                executor.submit(MathOperations.memory_intensive_task, size_per_thread)
                for _ in range(thread_count)
            ]
            
            # Collect results
            for future in as_completed(futures):
                results.append(future.result())
        
        return results
    
    def _multi_threaded_factorial(self, thread_count: int, n: int) -> int:
        """
        Execute factorial calculation using multiple threads.
        Note: This is for demonstration - actual factorial calculation
        doesn't parallelize well due to dependencies.
        """
        # For demonstration, we'll calculate multiple factorials in parallel
        # and return the largest one
        numbers = [n - i for i in range(thread_count)]
        results = []
        
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            futures = [
                executor.submit(MathOperations.large_factorial, num)
                for num in numbers if num > 0
            ]
            
            for future in as_completed(futures):
                results.append(future.result())
        
        return max(results) if results else 1
    
    def benchmark_performance(self, operation: str, thread_counts: List[int], 
                            *args, **kwargs) -> Dict[str, List[Dict[str, Any]]]:
        """
        Benchmark an operation across different thread counts.
        
        Args:
            operation: Operation to benchmark
            thread_counts: List of thread counts to test
            *args: Arguments for the operation
            **kwargs: Keyword arguments for the operation
            
        Returns:
            Dictionary containing performance data for each thread count
        """
        results = {"single_threaded": [], "multi_threaded": []}
        
        # Single-threaded baseline
        print(f"Running single-threaded {operation}...")
        single_result = self.execute_single_threaded(operation, *args, **kwargs)
        results["single_threaded"].append(single_result)
        
        # Multi-threaded tests
        for thread_count in thread_counts:
            if thread_count > 1:
                print(f"Running {operation} with {thread_count} threads...")
                multi_result = self.execute_multi_threaded(
                    operation, thread_count, *args, **kwargs
                )
                results["multi_threaded"].append(multi_result)
        
        # Store results for later analysis
        self.performance_data[operation] = results
        return results
    
    def get_thread_info(self) -> Dict[str, Any]:
        """Get information about current threading environment."""
        return {
            "active_threads": threading.active_count(),
            "current_thread": threading.current_thread().name,
            "max_workers": self.max_workers,
            "thread_list": [t.name for t in threading.enumerate()]
        }


# Example usage
if __name__ == "__main__":
    manager = ThreadingManager(max_workers=4)
    
    # Test prime search
    print("Testing prime search...")
    result = manager.benchmark_performance(
        "prime_search", [1, 2, 4], 1, 10000
    )
    
    print(f"Single-threaded time: {result['single_threaded'][0]['execution_time']:.4f}s")
    for multi_result in result['multi_threaded']:
        threads = multi_result['thread_count']
        time_taken = multi_result['execution_time']
        print(f"{threads} threads time: {time_taken:.4f}s")
    
    print(f"Thread info: {manager.get_thread_info()}")
