"""
Mathematical Operations Module

This module contains various mathematical operations that can be
executed in both single-threaded and multi-threaded modes to
demonstrate OS threading concepts.
"""

import math
import time
import numpy as np
from typing import List, Tuple, Any
from concurrent.futures import ThreadPoolExecutor, as_completed


class MathOperations:
    """Class containing various mathematical operations for threading demonstration."""
    
    @staticmethod
    def factorial_chunk(start: int, end: int) -> int:
        """Calculate factorial for a range of numbers (chunk processing)."""
        result = 1
        for i in range(start, end + 1):
            result *= i
        return result
    
    @staticmethod
    def large_factorial(n: int, chunk_size: int = 1000) -> int:
        """
        Calculate factorial of large numbers using chunked approach.
        This can be parallelized across multiple threads.
        """
        if n <= 1:
            return 1
        
        # For small numbers, use direct calculation
        if n <= chunk_size:
            return math.factorial(n)
        
        # For large numbers, we'll use a different approach
        # that's more suitable for threading demonstration
        result = 1
        for i in range(2, n + 1):
            result *= i
        return result
    
    @staticmethod
    def prime_check(num: int) -> bool:
        """Check if a number is prime."""
        if num < 2:
            return False
        if num == 2:
            return True
        if num % 2 == 0:
            return False
        
        for i in range(3, int(math.sqrt(num)) + 1, 2):
            if num % i == 0:
                return False
        return True
    
    @staticmethod
    def find_primes_in_range(start: int, end: int) -> List[int]:
        """Find all prime numbers in a given range."""
        primes = []
        for num in range(start, end + 1):
            if MathOperations.prime_check(num):
                primes.append(num)
        return primes
    
    @staticmethod
    def fibonacci_sequence(n: int) -> List[int]:
        """Generate Fibonacci sequence up to n terms."""
        if n <= 0:
            return []
        elif n == 1:
            return [0]
        elif n == 2:
            return [0, 1]
        
        fib = [0, 1]
        for i in range(2, n):
            fib.append(fib[i-1] + fib[i-2])
        return fib
    
    @staticmethod
    def matrix_multiply_chunk(matrix_a: np.ndarray, matrix_b: np.ndarray, 
                            row_start: int, row_end: int) -> np.ndarray:
        """Multiply a chunk of matrix A with matrix B."""
        return np.dot(matrix_a[row_start:row_end], matrix_b)
    
    @staticmethod
    def create_random_matrix(size: int, seed: int = None) -> np.ndarray:
        """Create a random matrix for testing purposes."""
        if seed:
            np.random.seed(seed)
        return np.random.randint(1, 100, size=(size, size))
    
    @staticmethod
    def cpu_intensive_task(iterations: int = 1000000) -> float:
        """
        A CPU-intensive task for performance testing.
        Calculates sum of squares for demonstration.
        """
        total = 0.0
        for i in range(iterations):
            total += i * i * math.sin(i) * math.cos(i)
        return total
    
    @staticmethod
    def memory_intensive_task(size_mb: int = 10) -> List[List[int]]:
        """
        A memory-intensive task for memory usage testing.
        Creates and manipulates large data structures.
        """
        # Create a large list of lists
        data = []
        elements_per_mb = size_mb * 1024 * 1024 // 8  # Approximate for integers
        
        for i in range(size_mb):
            chunk = list(range(elements_per_mb // size_mb))
            # Perform some operations on the chunk
            chunk = [x * 2 + 1 for x in chunk]
            data.append(chunk)
        
        return data


class TaskDistributor:
    """Utility class for distributing tasks across threads."""
    
    @staticmethod
    def split_range(start: int, end: int, num_chunks: int) -> List[Tuple[int, int]]:
        """Split a range into approximately equal chunks."""
        total_range = end - start + 1
        chunk_size = total_range // num_chunks
        remainder = total_range % num_chunks
        
        chunks = []
        current_start = start
        
        for i in range(num_chunks):
            current_chunk_size = chunk_size + (1 if i < remainder else 0)
            current_end = current_start + current_chunk_size - 1
            chunks.append((current_start, current_end))
            current_start = current_end + 1
        
        return chunks
    
    @staticmethod
    def split_list(data: List[Any], num_chunks: int) -> List[List[Any]]:
        """Split a list into approximately equal chunks."""
        chunk_size = len(data) // num_chunks
        remainder = len(data) % num_chunks
        
        chunks = []
        start_idx = 0
        
        for i in range(num_chunks):
            current_chunk_size = chunk_size + (1 if i < remainder else 0)
            end_idx = start_idx + current_chunk_size
            chunks.append(data[start_idx:end_idx])
            start_idx = end_idx
        
        return chunks


# Example usage and testing functions
if __name__ == "__main__":
    # Test basic operations
    print("Testing Mathematical Operations...")
    
    # Test factorial
    print(f"Factorial of 10: {MathOperations.large_factorial(10)}")
    
    # Test prime finding
    primes = MathOperations.find_primes_in_range(1, 100)
    print(f"Primes from 1 to 100: {len(primes)} found")
    
    # Test Fibonacci
    fib = MathOperations.fibonacci_sequence(10)
    print(f"First 10 Fibonacci numbers: {fib}")
    
    # Test task distribution
    chunks = TaskDistributor.split_range(1, 1000, 4)
    print(f"Range 1-1000 split into 4 chunks: {chunks}")
