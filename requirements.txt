# Multi-Threaded Calculator Dependencies

# Core dependencies
psutil>=5.9.0          # System and process monitoring
numpy>=1.21.0          # Numerical computations
matplotlib>=3.5.0      # Data visualization
pandas>=1.3.0          # Data analysis and export

# Development and testing
pytest>=7.0.0          # Unit testing framework
pytest-cov>=4.0.0     # Coverage reporting
black>=22.0.0          # Code formatting
flake8>=5.0.0          # Code linting

# Documentation
sphinx>=5.0.0          # Documentation generation
sphinx-rtd-theme>=1.0.0 # Documentation theme

# Optional: Enhanced performance monitoring
memory-profiler>=0.60.0  # Memory usage profiling
py-spy>=0.3.0           # CPU profiling (install separately if needed)
