"""
Performance Monitor Module

This module provides comprehensive performance monitoring capabilities
for tracking CPU usage, memory consumption, and execution metrics
across different threading scenarios.
"""

import time
import psutil
import threading
import json
import csv
from typing import Dict, List, Any, Optional
from datetime import datetime
import matplotlib.pyplot as plt
import pandas as pd


class PerformanceMonitor:
    """Monitors and records performance metrics during program execution."""
    
    def __init__(self, sampling_interval: float = 0.1):
        """
        Initialize the performance monitor.
        
        Args:
            sampling_interval: Time interval between performance samples (seconds)
        """
        self.sampling_interval = sampling_interval
        self.monitoring = False
        self.monitor_thread = None
        self.performance_data = []
        self.start_time = None
        self.process = psutil.Process()
        
    def start_monitoring(self) -> None:
        """Start performance monitoring in a separate thread."""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.start_time = time.time()
        self.performance_data = []
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
    def stop_monitoring(self) -> Dict[str, Any]:
        """
        Stop performance monitoring and return collected data.
        
        Returns:
            Dictionary containing performance statistics
        """
        if not self.monitoring:
            return {}
            
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
            
        return self._calculate_statistics()
    
    def _monitor_loop(self) -> None:
        """Main monitoring loop that runs in a separate thread."""
        while self.monitoring:
            try:
                # Get current timestamp
                current_time = time.time()
                elapsed_time = current_time - self.start_time
                
                # Collect system metrics
                cpu_percent = psutil.cpu_percent(interval=None)
                memory_info = self.process.memory_info()
                memory_percent = self.process.memory_percent()
                
                # Get per-CPU usage
                cpu_per_core = psutil.cpu_percent(interval=None, percpu=True)
                
                # Thread information
                thread_count = threading.active_count()
                
                # Store the data point
                data_point = {
                    'timestamp': current_time,
                    'elapsed_time': elapsed_time,
                    'cpu_percent_total': cpu_percent,
                    'cpu_per_core': cpu_per_core,
                    'memory_rss_mb': memory_info.rss / (1024 * 1024),  # Convert to MB
                    'memory_vms_mb': memory_info.vms / (1024 * 1024),  # Convert to MB
                    'memory_percent': memory_percent,
                    'thread_count': thread_count,
                    'process_id': self.process.pid
                }
                
                self.performance_data.append(data_point)
                
            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                
            time.sleep(self.sampling_interval)
    
    def _calculate_statistics(self) -> Dict[str, Any]:
        """Calculate performance statistics from collected data."""
        if not self.performance_data:
            return {}
        
        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(self.performance_data)
        
        stats = {
            'total_duration': df['elapsed_time'].max(),
            'sample_count': len(df),
            'cpu_stats': {
                'mean': df['cpu_percent_total'].mean(),
                'max': df['cpu_percent_total'].max(),
                'min': df['cpu_percent_total'].min(),
                'std': df['cpu_percent_total'].std()
            },
            'memory_stats': {
                'mean_rss_mb': df['memory_rss_mb'].mean(),
                'max_rss_mb': df['memory_rss_mb'].max(),
                'min_rss_mb': df['memory_rss_mb'].min(),
                'mean_percent': df['memory_percent'].mean(),
                'max_percent': df['memory_percent'].max()
            },
            'thread_stats': {
                'mean_count': df['thread_count'].mean(),
                'max_count': df['thread_count'].max(),
                'min_count': df['thread_count'].min()
            },
            'raw_data': self.performance_data
        }
        
        return stats
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get comprehensive system information."""
        return {
            'cpu_info': {
                'physical_cores': psutil.cpu_count(logical=False),
                'logical_cores': psutil.cpu_count(logical=True),
                'cpu_freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                'cpu_percent': psutil.cpu_percent(interval=1)
            },
            'memory_info': {
                'total_gb': psutil.virtual_memory().total / (1024**3),
                'available_gb': psutil.virtual_memory().available / (1024**3),
                'used_percent': psutil.virtual_memory().percent
            },
            'process_info': {
                'pid': self.process.pid,
                'name': self.process.name(),
                'status': self.process.status(),
                'create_time': datetime.fromtimestamp(self.process.create_time()).isoformat()
            },
            'platform_info': {
                'system': psutil.WINDOWS if hasattr(psutil, 'WINDOWS') else 'Linux',
                'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat()
            }
        }
    
    def export_to_csv(self, filename: str) -> None:
        """Export performance data to CSV file."""
        if not self.performance_data:
            print("No performance data to export")
            return
            
        df = pd.DataFrame(self.performance_data)
        df.to_csv(filename, index=False)
        print(f"Performance data exported to {filename}")
    
    def export_to_json(self, filename: str, include_stats: bool = True) -> None:
        """Export performance data and statistics to JSON file."""
        export_data = {
            'system_info': self.get_system_info(),
            'raw_data': self.performance_data
        }
        
        if include_stats and self.performance_data:
            export_data['statistics'] = self._calculate_statistics()
        
        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        print(f"Performance data exported to {filename}")
    
    def create_performance_plots(self, output_dir: str = "plots") -> List[str]:
        """
        Create performance visualization plots.
        
        Args:
            output_dir: Directory to save plots
            
        Returns:
            List of created plot filenames
        """
        if not self.performance_data:
            print("No performance data to plot")
            return []
        
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        df = pd.DataFrame(self.performance_data)
        plot_files = []
        
        # CPU Usage Plot
        plt.figure(figsize=(12, 6))
        plt.subplot(2, 2, 1)
        plt.plot(df['elapsed_time'], df['cpu_percent_total'])
        plt.title('CPU Usage Over Time')
        plt.xlabel('Time (seconds)')
        plt.ylabel('CPU Usage (%)')
        plt.grid(True)
        
        # Memory Usage Plot
        plt.subplot(2, 2, 2)
        plt.plot(df['elapsed_time'], df['memory_rss_mb'])
        plt.title('Memory Usage Over Time')
        plt.xlabel('Time (seconds)')
        plt.ylabel('Memory (MB)')
        plt.grid(True)
        
        # Thread Count Plot
        plt.subplot(2, 2, 3)
        plt.plot(df['elapsed_time'], df['thread_count'])
        plt.title('Thread Count Over Time')
        plt.xlabel('Time (seconds)')
        plt.ylabel('Number of Threads')
        plt.grid(True)
        
        # Memory Percentage Plot
        plt.subplot(2, 2, 4)
        plt.plot(df['elapsed_time'], df['memory_percent'])
        plt.title('Memory Usage Percentage')
        plt.xlabel('Time (seconds)')
        plt.ylabel('Memory Usage (%)')
        plt.grid(True)
        
        plt.tight_layout()
        plot_file = os.path.join(output_dir, 'performance_overview.png')
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        plot_files.append(plot_file)
        
        return plot_files


class BenchmarkRunner:
    """Utility class for running performance benchmarks."""
    
    def __init__(self):
        self.results = []
    
    def run_benchmark(self, name: str, operation_func, *args, **kwargs) -> Dict[str, Any]:
        """
        Run a single benchmark with performance monitoring.
        
        Args:
            name: Name of the benchmark
            operation_func: Function to benchmark
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            Benchmark results including performance data
        """
        monitor = PerformanceMonitor(sampling_interval=0.05)
        
        print(f"Running benchmark: {name}")
        
        # Start monitoring
        monitor.start_monitoring()
        start_time = time.time()
        
        try:
            # Execute the operation
            result = operation_func(*args, **kwargs)
            success = True
            error = None
        except Exception as e:
            result = None
            success = False
            error = str(e)
        
        end_time = time.time()
        
        # Stop monitoring and get performance data
        performance_stats = monitor.stop_monitoring()
        
        benchmark_result = {
            'name': name,
            'success': success,
            'error': error,
            'execution_time': end_time - start_time,
            'result_size': len(str(result)) if result else 0,
            'performance_stats': performance_stats,
            'system_info': monitor.get_system_info(),
            'timestamp': datetime.now().isoformat()
        }
        
        self.results.append(benchmark_result)
        return benchmark_result
    
    def export_results(self, filename: str) -> None:
        """Export all benchmark results to a file."""
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        print(f"Benchmark results exported to {filename}")


# Example usage
if __name__ == "__main__":
    # Test the performance monitor
    monitor = PerformanceMonitor()
    
    print("Testing performance monitor...")
    monitor.start_monitoring()
    
    # Simulate some work
    time.sleep(2)
    total = sum(i*i for i in range(100000))
    
    stats = monitor.stop_monitoring()
    print(f"Performance stats: {stats['cpu_stats']}")
    print(f"System info: {monitor.get_system_info()}")
