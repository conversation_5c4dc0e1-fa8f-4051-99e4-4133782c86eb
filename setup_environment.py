#!/usr/bin/env python3
"""
Environment Setup Script for Multi-Threaded Calculator

This script checks and sets up the development environment for both
Windows and Linux platforms.
"""

import sys
import platform
import subprocess
import importlib.util

def check_python_version():
    """Check if Python version is 3.8 or higher."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required. Current version: {version.major}.{version.minor}")
        return False
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_required_packages():
    """Check if required packages are installed."""
    required_packages = [
        'psutil', 'numpy', 'matplotlib', 'pandas'
    ]
    
    missing_packages = []
    for package in required_packages:
        if importlib.util.find_spec(package) is None:
            missing_packages.append(package)
        else:
            print(f"✅ {package} is installed")
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        return False, missing_packages
    
    return True, []

def install_packages(packages):
    """Install missing packages using pip."""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + packages)
        print(f"✅ Successfully installed: {', '.join(packages)}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def check_system_info():
    """Display system information."""
    print("\n=== System Information ===")
    print(f"Operating System: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.architecture()[0]}")
    print(f"Processor: {platform.processor()}")
    print(f"Python Executable: {sys.executable}")

def main():
    """Main setup function."""
    print("🚀 Setting up Multi-Threaded Calculator Environment\n")
    
    # Check system info
    check_system_info()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check required packages
    packages_ok, missing = check_required_packages()
    
    if not packages_ok:
        print(f"\n📦 Installing missing packages...")
        if install_packages(missing):
            print("✅ All packages installed successfully!")
        else:
            print("❌ Package installation failed. Please install manually:")
            print(f"pip install {' '.join(missing)}")
            sys.exit(1)
    
    print("\n🎉 Environment setup complete!")
    print("\nNext steps:")
    print("1. Run the calculator: python multithreaded_calculator/main.py")
    print("2. Run tests: python -m pytest multithreaded_calculator/tests/")

if __name__ == "__main__":
    main()
