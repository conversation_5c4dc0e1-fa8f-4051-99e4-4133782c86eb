#!/usr/bin/env python3
"""
Performance Demonstration Script

This script demonstrates the multi-threaded calculator's performance
monitoring capabilities and shows the difference between single-threaded
and multi-threaded execution.
"""

import sys
import os
import time
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from multithreaded_calculator.calculator.threading_manager import ThreadingManager
from multithreaded_calculator.calculator.performance_monitor import PerformanceMonitor


def demo_prime_search():
    """Demonstrate prime search with performance monitoring."""
    print("🔍 PRIME SEARCH PERFORMANCE DEMO")
    print("=" * 50)
    
    manager = ThreadingManager(max_workers=4)
    
    # Test parameters
    start_range = 1
    end_range = 20000
    thread_counts = [1, 2, 4]
    
    print(f"Searching for primes from {start_range} to {end_range}")
    print("-" * 50)
    
    results = {}
    
    for thread_count in thread_counts:
        print(f"\nTesting with {thread_count} thread(s)...")
        
        # Create performance monitor
        monitor = PerformanceMonitor(sampling_interval=0.1)
        
        # Start monitoring
        monitor.start_monitoring()
        start_time = time.time()
        
        if thread_count == 1:
            result = manager.execute_single_threaded("prime_search", start_range, end_range)
        else:
            result = manager.execute_multi_threaded("prime_search", thread_count, start_range, end_range)
        
        end_time = time.time()
        
        # Stop monitoring
        perf_stats = monitor.stop_monitoring()
        
        # Store results
        results[thread_count] = {
            'execution_time': result['execution_time'],
            'prime_count': len(result['result']),
            'performance_stats': perf_stats
        }
        
        # Display results
        print(f"  Execution time: {result['execution_time']:.4f}s")
        print(f"  Primes found: {len(result['result'])}")
        if perf_stats:
            print(f"  Avg CPU usage: {perf_stats['cpu_stats']['mean']:.1f}%")
            print(f"  Max memory: {perf_stats['memory_stats']['max_rss_mb']:.1f} MB")
    
    # Calculate and display speedup
    print("\n📊 PERFORMANCE COMPARISON")
    print("-" * 50)
    single_time = results[1]['execution_time']
    
    for thread_count in thread_counts:
        if thread_count == 1:
            print(f"Single-threaded: {single_time:.4f}s (baseline)")
        else:
            time_taken = results[thread_count]['execution_time']
            speedup = single_time / time_taken if time_taken > 0 else 0
            efficiency = speedup / thread_count * 100
            print(f"{thread_count:2d} threads: {time_taken:.4f}s "
                  f"(speedup: {speedup:.2f}x, efficiency: {efficiency:.1f}%)")
    
    return results


def demo_cpu_intensive():
    """Demonstrate CPU-intensive task with performance monitoring."""
    print("\n⚡ CPU INTENSIVE PERFORMANCE DEMO")
    print("=" * 50)
    
    manager = ThreadingManager(max_workers=4)
    
    # Test parameters
    iterations = 200000
    thread_counts = [1, 2, 4]
    
    print(f"CPU intensive task with {iterations:,} iterations")
    print("-" * 50)
    
    results = {}
    
    for thread_count in thread_counts:
        print(f"\nTesting with {thread_count} thread(s)...")
        
        # Create performance monitor
        monitor = PerformanceMonitor(sampling_interval=0.05)
        
        # Start monitoring
        monitor.start_monitoring()
        
        if thread_count == 1:
            result = manager.execute_single_threaded("cpu_intensive", iterations)
        else:
            result = manager.execute_multi_threaded("cpu_intensive", thread_count, iterations)
        
        # Stop monitoring
        perf_stats = monitor.stop_monitoring()
        
        # Store results
        results[thread_count] = {
            'execution_time': result['execution_time'],
            'result_value': result['result'],
            'performance_stats': perf_stats
        }
        
        # Display results
        print(f"  Execution time: {result['execution_time']:.4f}s")
        print(f"  Result value: {result['result']:.2e}")
        if perf_stats:
            print(f"  Avg CPU usage: {perf_stats['cpu_stats']['mean']:.1f}%")
            print(f"  Max CPU usage: {perf_stats['cpu_stats']['max']:.1f}%")
    
    # Calculate and display speedup
    print("\n📊 PERFORMANCE COMPARISON")
    print("-" * 50)
    single_time = results[1]['execution_time']
    
    for thread_count in thread_counts:
        if thread_count == 1:
            print(f"Single-threaded: {single_time:.4f}s (baseline)")
        else:
            time_taken = results[thread_count]['execution_time']
            speedup = single_time / time_taken if time_taken > 0 else 0
            efficiency = speedup / thread_count * 100
            print(f"{thread_count:2d} threads: {time_taken:.4f}s "
                  f"(speedup: {speedup:.2f}x, efficiency: {efficiency:.1f}%)")
    
    return results


def display_system_info():
    """Display system information."""
    monitor = PerformanceMonitor()
    system_info = monitor.get_system_info()
    
    print("💻 SYSTEM INFORMATION")
    print("=" * 50)
    print(f"Physical CPU Cores: {system_info['cpu_info']['physical_cores']}")
    print(f"Logical CPU Cores: {system_info['cpu_info']['logical_cores']}")
    print(f"Total Memory: {system_info['memory_info']['total_gb']:.2f} GB")
    print(f"Available Memory: {system_info['memory_info']['available_gb']:.2f} GB")
    print(f"Process ID: {system_info['process_info']['pid']}")


def main():
    """Main demonstration function."""
    print("🚀 MULTI-THREADED CALCULATOR PERFORMANCE DEMONSTRATION")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Display system information
    display_system_info()
    print()
    
    try:
        # Run prime search demo
        prime_results = demo_prime_search()
        
        # Run CPU intensive demo
        cpu_results = demo_cpu_intensive()
        
        print("\n✅ DEMONSTRATION COMPLETED SUCCESSFULLY!")
        print("\nKey Observations:")
        print("- Multi-threading can improve performance for parallelizable tasks")
        print("- Efficiency depends on task type and system resources")
        print("- Performance monitoring helps identify bottlenecks")
        print("- Thread overhead may reduce efficiency with too many threads")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Demonstration interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
