#!/usr/bin/env python3
"""
Multi-Threaded Calculator - Main Application

This is the main entry point for the multi-threaded calculator application
that demonstrates operating system threading concepts through mathematical
computations with comprehensive performance monitoring.

Author: Final Assignment Team
Course: BIT1213/BCC1213 Operating System
"""

import os
import sys
import argparse
import platform
from datetime import datetime
from typing import Dict, Any

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from multithreaded_calculator.calculator.operations import MathOperations
from multithreaded_calculator.calculator.threading_manager import ThreadingManager
from multithreaded_calculator.calculator.performance_monitor import PerformanceMonitor, BenchmarkRunner


class MultiThreadedCalculator:
    """Main application class for the multi-threaded calculator."""
    
    def __init__(self):
        self.threading_manager = ThreadingManager()
        self.benchmark_runner = BenchmarkRunner()
        self.results_dir = self._setup_results_directory()
        
    def _setup_results_directory(self) -> str:
        """Setup results directory based on current OS."""
        base_dir = os.path.join(os.path.dirname(__file__), "results")
        
        if platform.system().lower() == "windows":
            results_dir = os.path.join(base_dir, "windows_results")
        else:
            results_dir = os.path.join(base_dir, "linux_results")
            
        os.makedirs(results_dir, exist_ok=True)
        return results_dir
    
    def display_system_info(self) -> None:
        """Display comprehensive system information."""
        monitor = PerformanceMonitor()
        system_info = monitor.get_system_info()
        
        print("=" * 60)
        print("SYSTEM INFORMATION")
        print("=" * 60)
        print(f"Operating System: {platform.system()} {platform.release()}")
        print(f"Architecture: {platform.architecture()[0]}")
        print(f"Processor: {platform.processor()}")
        print(f"Physical CPU Cores: {system_info['cpu_info']['physical_cores']}")
        print(f"Logical CPU Cores: {system_info['cpu_info']['logical_cores']}")
        print(f"Total Memory: {system_info['memory_info']['total_gb']:.2f} GB")
        print(f"Available Memory: {system_info['memory_info']['available_gb']:.2f} GB")
        print(f"Python Version: {sys.version}")
        print(f"Process ID: {system_info['process_info']['pid']}")
        print("=" * 60)
    
    def run_prime_search_benchmark(self, start: int = 1, end: int = 100000, 
                                 thread_counts: list = None) -> Dict[str, Any]:
        """Run prime search benchmark with different thread counts."""
        if thread_counts is None:
            thread_counts = [1, 2, 4, 8]
            
        print(f"\n🔍 PRIME SEARCH BENCHMARK")
        print(f"Range: {start} to {end}")
        print("-" * 40)
        
        results = self.threading_manager.benchmark_performance(
            "prime_search", thread_counts, start, end
        )
        
        # Display results
        single_time = results["single_threaded"][0]["execution_time"]
        prime_count = len(results["single_threaded"][0]["result"])
        
        print(f"Primes found: {prime_count}")
        print(f"Single-threaded time: {single_time:.4f}s")
        
        for result in results["multi_threaded"]:
            threads = result["thread_count"]
            time_taken = result["execution_time"]
            speedup = single_time / time_taken if time_taken > 0 else 0
            efficiency = speedup / threads * 100
            
            print(f"{threads:2d} threads: {time_taken:.4f}s "
                  f"(speedup: {speedup:.2f}x, efficiency: {efficiency:.1f}%)")
        
        return results
    
    def run_cpu_intensive_benchmark(self, iterations: int = 1000000,
                                  thread_counts: list = None) -> Dict[str, Any]:
        """Run CPU-intensive benchmark with different thread counts."""
        if thread_counts is None:
            thread_counts = [1, 2, 4, 8]
            
        print(f"\n⚡ CPU INTENSIVE BENCHMARK")
        print(f"Iterations: {iterations:,}")
        print("-" * 40)
        
        results = self.threading_manager.benchmark_performance(
            "cpu_intensive", thread_counts, iterations
        )
        
        # Display results
        single_time = results["single_threaded"][0]["execution_time"]
        
        print(f"Single-threaded time: {single_time:.4f}s")
        
        for result in results["multi_threaded"]:
            threads = result["thread_count"]
            time_taken = result["execution_time"]
            speedup = single_time / time_taken if time_taken > 0 else 0
            efficiency = speedup / threads * 100
            
            print(f"{threads:2d} threads: {time_taken:.4f}s "
                  f"(speedup: {speedup:.2f}x, efficiency: {efficiency:.1f}%)")
        
        return results
    
    def run_memory_intensive_benchmark(self, size_mb: int = 50,
                                     thread_counts: list = None) -> Dict[str, Any]:
        """Run memory-intensive benchmark with different thread counts."""
        if thread_counts is None:
            thread_counts = [1, 2, 4]
            
        print(f"\n💾 MEMORY INTENSIVE BENCHMARK")
        print(f"Memory size: {size_mb} MB")
        print("-" * 40)
        
        results = self.threading_manager.benchmark_performance(
            "memory_intensive", thread_counts, size_mb
        )
        
        # Display results
        single_time = results["single_threaded"][0]["execution_time"]
        
        print(f"Single-threaded time: {single_time:.4f}s")
        
        for result in results["multi_threaded"]:
            threads = result["thread_count"]
            time_taken = result["execution_time"]
            speedup = single_time / time_taken if time_taken > 0 else 0
            efficiency = speedup / threads * 100
            
            print(f"{threads:2d} threads: {time_taken:.4f}s "
                  f"(speedup: {speedup:.2f}x, efficiency: {efficiency:.1f}%)")
        
        return results
    
    def run_comprehensive_benchmark(self) -> None:
        """Run all benchmarks and save results."""
        print("\n🚀 STARTING COMPREHENSIVE BENCHMARK SUITE")
        print("=" * 60)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Run all benchmarks
        prime_results = self.run_prime_search_benchmark(1, 50000, [1, 2, 4, 8])
        cpu_results = self.run_cpu_intensive_benchmark(500000, [1, 2, 4, 8])
        memory_results = self.run_memory_intensive_benchmark(30, [1, 2, 4])
        
        # Compile comprehensive results
        comprehensive_results = {
            "timestamp": timestamp,
            "system_info": PerformanceMonitor().get_system_info(),
            "benchmarks": {
                "prime_search": prime_results,
                "cpu_intensive": cpu_results,
                "memory_intensive": memory_results
            }
        }
        
        # Save results
        results_file = os.path.join(self.results_dir, f"benchmark_results_{timestamp}.json")
        
        import json
        with open(results_file, 'w') as f:
            json.dump(comprehensive_results, f, indent=2, default=str)
        
        print(f"\n📊 Results saved to: {results_file}")
        print("\n✅ BENCHMARK SUITE COMPLETED")
    
    def interactive_mode(self) -> None:
        """Run the calculator in interactive mode."""
        print("\n🧮 INTERACTIVE MODE")
        print("Available operations:")
        print("1. Prime Search")
        print("2. CPU Intensive Task")
        print("3. Memory Intensive Task")
        print("4. Run All Benchmarks")
        print("5. Exit")
        
        while True:
            try:
                choice = input("\nSelect operation (1-5): ").strip()
                
                if choice == "1":
                    start = int(input("Start range (default 1): ") or "1")
                    end = int(input("End range (default 10000): ") or "10000")
                    self.run_prime_search_benchmark(start, end)
                    
                elif choice == "2":
                    iterations = int(input("Iterations (default 500000): ") or "500000")
                    self.run_cpu_intensive_benchmark(iterations)
                    
                elif choice == "3":
                    size_mb = int(input("Memory size in MB (default 20): ") or "20")
                    self.run_memory_intensive_benchmark(size_mb)
                    
                elif choice == "4":
                    self.run_comprehensive_benchmark()
                    
                elif choice == "5":
                    print("Goodbye! 👋")
                    break
                    
                else:
                    print("Invalid choice. Please select 1-5.")
                    
            except KeyboardInterrupt:
                print("\n\nExiting... 👋")
                break
            except ValueError as e:
                print(f"Invalid input: {e}")
            except Exception as e:
                print(f"Error: {e}")


def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(
        description="Multi-Threaded Calculator - OS Threading Demonstration"
    )
    parser.add_argument(
        "--mode", 
        choices=["interactive", "benchmark", "info"],
        default="interactive",
        help="Run mode (default: interactive)"
    )
    parser.add_argument(
        "--prime-range",
        type=int,
        nargs=2,
        default=[1, 50000],
        help="Prime search range (default: 1 50000)"
    )
    parser.add_argument(
        "--cpu-iterations",
        type=int,
        default=500000,
        help="CPU intensive iterations (default: 500000)"
    )
    parser.add_argument(
        "--memory-size",
        type=int,
        default=30,
        help="Memory intensive size in MB (default: 30)"
    )
    
    args = parser.parse_args()
    
    # Create calculator instance
    calculator = MultiThreadedCalculator()
    
    # Display system information
    calculator.display_system_info()
    
    if args.mode == "info":
        return
    elif args.mode == "benchmark":
        calculator.run_comprehensive_benchmark()
    else:
        calculator.interactive_mode()


if __name__ == "__main__":
    main()
