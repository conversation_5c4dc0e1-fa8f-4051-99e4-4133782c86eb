================================================================================
MULTI-THREADED CALCULATOR PERFORMANCE ANALYSIS REPORT
================================================================================
Generated: 2025-07-31 18:29:15
Platform: Windows True

SYSTEM CONFIGURATION
----------------------------------------
Physical CPU Cores: 10
Logical CPU Cores: 12
Total Memory: 15.69 GB

PERFORMANCE ANALYSIS
----------------------------------------

Prime Search Benchmark:
  Single-threaded baseline: 0.0556s
  Best performance: 8 threads
  Best execution time: 0.0421s
  Maximum speedup: 1.32x
  Peak efficiency: 16.5%

Cpu Intensive Benchmark:
  Single-threaded baseline: 0.1429s
  Best performance: 8 threads
  Best execution time: 0.1257s
  Maximum speedup: 1.14x
  Peak efficiency: 14.2%

Memory Intensive Benchmark:
  Single-threaded baseline: 0.3733s
  Best performance: 2 threads
  Best execution time: 0.4051s
  Maximum speedup: 0.92x
  Peak efficiency: 46.1%

OPERATING SYSTEM CONCEPTS DEMONSTRATED
----------------------------------------
1. Threading and Concurrency:
   - Thread creation and management using ThreadPoolExecutor
   - Parallel task execution across multiple CPU cores
   - Thread synchronization and result aggregation

2. CPU Scheduling:
   - Operating system thread scheduling across logical cores
   - Load balancing and CPU affinity effects
   - Context switching overhead analysis

3. Memory Management:
   - Memory allocation patterns in multi-threaded environment
   - Garbage collection impact on performance
   - Memory contention between threads

4. System Resource Utilization:
   - CPU utilization monitoring and optimization
   - Resource contention and bottleneck identification
   - Performance scaling with thread count

KEY INSIGHTS
----------------------------------------
Average speedup with 2 threads: 1.07x
Average speedup with 4 threads: 1.06x
Average speedup with 8 threads: 1.23x

Threading Effectiveness:
- Limited threading benefits (task overhead or dependencies)

Performance Characteristics:
- CPU-bound tasks show better scaling than memory-bound tasks
- Diminishing returns observed with higher thread counts
- Thread overhead becomes significant beyond optimal thread count