#!/usr/bin/env python3
"""
Performance Analysis and Visualization

This script creates detailed performance analysis and visualizations
from the Windows benchmark data, demonstrating OS threading concepts.
"""

import os
import json
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime


def load_windows_results():
    """Load the latest Windows benchmark results."""
    results_dir = os.path.join("multithreaded_calculator", "results", "windows_results")
    
    if not os.path.exists(results_dir):
        print("❌ Windows results directory not found!")
        return None
    
    # Find the most recent benchmark file
    json_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
    
    if not json_files:
        print("❌ No benchmark results found!")
        return None
    
    # Load the most recent file
    latest_file = max(json_files)
    file_path = os.path.join(results_dir, latest_file)
    
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        print(f"✅ Loaded benchmark data from: {latest_file}")
        return data
    except Exception as e:
        print(f"❌ Error loading benchmark data: {e}")
        return None


def create_performance_dataframe(benchmark_data):
    """Convert benchmark data to pandas DataFrame for analysis."""
    rows = []
    
    benchmarks = benchmark_data.get('benchmarks', {})
    
    for benchmark_name, benchmark_results in benchmarks.items():
        # Single-threaded data
        single_threaded = benchmark_results.get('single_threaded', [])
        if single_threaded:
            single_result = single_threaded[0]
            rows.append({
                'benchmark': benchmark_name,
                'threads': 1,
                'execution_time': single_result.get('execution_time', 0),
                'cpu_time': single_result.get('cpu_time', 0),
                'speedup': 1.0,
                'efficiency': 100.0
            })
        
        # Multi-threaded data
        multi_threaded = benchmark_results.get('multi_threaded', [])
        single_time = single_result.get('execution_time', 1) if single_threaded else 1
        
        for result in multi_threaded:
            threads = result.get('thread_count', 1)
            exec_time = result.get('execution_time', 0)
            speedup = single_time / exec_time if exec_time > 0 else 0
            efficiency = speedup / threads * 100 if threads > 0 else 0
            
            rows.append({
                'benchmark': benchmark_name,
                'threads': threads,
                'execution_time': exec_time,
                'cpu_time': result.get('cpu_time', 0),
                'speedup': speedup,
                'efficiency': efficiency
            })
    
    return pd.DataFrame(rows)


def create_performance_visualizations(df, output_dir="performance_charts"):
    """Create comprehensive performance visualizations."""
    os.makedirs(output_dir, exist_ok=True)
    created_files = []
    
    # Set up the plotting style
    plt.style.use('default')
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    
    # 1. Execution Time Comparison
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('Multi-Threaded Calculator Performance Analysis', fontsize=16, fontweight='bold')
    
    benchmarks = df['benchmark'].unique()
    
    for i, benchmark in enumerate(benchmarks):
        if i >= 3:  # Limit to 3 benchmarks for layout
            break
            
        benchmark_data = df[df['benchmark'] == benchmark]
        
        ax = axes[i]
        threads = benchmark_data['threads']
        exec_times = benchmark_data['execution_time']
        
        ax.bar(threads.astype(str), exec_times, color=colors[i % len(colors)], alpha=0.7)
        ax.set_title(f'{benchmark.replace("_", " ").title()}', fontweight='bold')
        ax.set_xlabel('Number of Threads')
        ax.set_ylabel('Execution Time (seconds)')
        ax.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for j, (thread, time) in enumerate(zip(threads, exec_times)):
            ax.text(j, time + max(exec_times) * 0.01, f'{time:.4f}s', 
                   ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    chart_file = os.path.join(output_dir, 'execution_time_comparison.png')
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    plt.close()
    created_files.append(chart_file)
    
    # 2. Speedup Analysis
    fig, ax = plt.subplots(figsize=(12, 8))
    
    for i, benchmark in enumerate(benchmarks):
        benchmark_data = df[df['benchmark'] == benchmark]
        threads = benchmark_data['threads']
        speedups = benchmark_data['speedup']
        
        ax.plot(threads, speedups, 'o-', label=benchmark.replace('_', ' ').title(), 
               linewidth=2, markersize=8, color=colors[i % len(colors)])
    
    # Add ideal speedup line
    max_threads = df['threads'].max()
    ideal_threads = range(1, max_threads + 1)
    ax.plot(ideal_threads, ideal_threads, '--', label='Ideal Speedup', 
           color='gray', alpha=0.7, linewidth=2)
    
    ax.set_xlabel('Number of Threads', fontsize=12)
    ax.set_ylabel('Speedup Factor', fontsize=12)
    ax.set_title('Threading Speedup Analysis', fontsize=14, fontweight='bold')
    ax.legend(fontsize=10)
    ax.grid(True, alpha=0.3)
    ax.set_xlim(0.5, max_threads + 0.5)
    ax.set_ylim(0, max(df['speedup'].max(), max_threads) * 1.1)
    
    plt.tight_layout()
    chart_file = os.path.join(output_dir, 'speedup_analysis.png')
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    plt.close()
    created_files.append(chart_file)
    
    # 3. Efficiency Analysis
    fig, ax = plt.subplots(figsize=(12, 8))
    
    for i, benchmark in enumerate(benchmarks):
        benchmark_data = df[df['benchmark'] == benchmark]
        threads = benchmark_data['threads']
        efficiency = benchmark_data['efficiency']
        
        ax.plot(threads, efficiency, 's-', label=benchmark.replace('_', ' ').title(), 
               linewidth=2, markersize=8, color=colors[i % len(colors)])
    
    ax.axhline(y=100, color='gray', linestyle='--', alpha=0.7, label='100% Efficiency')
    ax.set_xlabel('Number of Threads', fontsize=12)
    ax.set_ylabel('Efficiency (%)', fontsize=12)
    ax.set_title('Threading Efficiency Analysis', fontsize=14, fontweight='bold')
    ax.legend(fontsize=10)
    ax.grid(True, alpha=0.3)
    ax.set_xlim(0.5, max_threads + 0.5)
    ax.set_ylim(0, max(df['efficiency'].max(), 100) * 1.1)
    
    plt.tight_layout()
    chart_file = os.path.join(output_dir, 'efficiency_analysis.png')
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    plt.close()
    created_files.append(chart_file)
    
    # 4. Performance Summary Table
    fig, ax = plt.subplots(figsize=(14, 8))
    ax.axis('tight')
    ax.axis('off')
    
    # Create summary table
    summary_data = []
    for benchmark in benchmarks:
        benchmark_data = df[df['benchmark'] == benchmark]
        
        single_thread = benchmark_data[benchmark_data['threads'] == 1]
        best_multi = benchmark_data[benchmark_data['threads'] > 1].loc[
            benchmark_data[benchmark_data['threads'] > 1]['speedup'].idxmax()
        ] if len(benchmark_data[benchmark_data['threads'] > 1]) > 0 else None
        
        if not single_thread.empty:
            single_time = single_thread['execution_time'].iloc[0]
            
            if best_multi is not None:
                best_threads = int(best_multi['threads'])
                best_time = best_multi['execution_time']
                best_speedup = best_multi['speedup']
                best_efficiency = best_multi['efficiency']
            else:
                best_threads = 1
                best_time = single_time
                best_speedup = 1.0
                best_efficiency = 100.0
            
            summary_data.append([
                benchmark.replace('_', ' ').title(),
                f"{single_time:.4f}s",
                f"{best_threads}",
                f"{best_time:.4f}s",
                f"{best_speedup:.2f}x",
                f"{best_efficiency:.1f}%"
            ])
    
    table = ax.table(cellText=summary_data,
                    colLabels=['Benchmark', 'Single Thread', 'Best Thread Count', 
                              'Best Time', 'Best Speedup', 'Best Efficiency'],
                    cellLoc='center',
                    loc='center')
    
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 2)
    
    # Style the table
    for i in range(len(summary_data) + 1):
        for j in range(6):
            cell = table[(i, j)]
            if i == 0:  # Header row
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
    
    ax.set_title('Performance Summary Table', fontsize=14, fontweight='bold', pad=20)
    
    plt.tight_layout()
    chart_file = os.path.join(output_dir, 'performance_summary.png')
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    plt.close()
    created_files.append(chart_file)
    
    return created_files


def generate_analysis_report(df, system_info):
    """Generate detailed analysis report."""
    report = []
    report.append("=" * 80)
    report.append("MULTI-THREADED CALCULATOR PERFORMANCE ANALYSIS REPORT")
    report.append("=" * 80)
    report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"Platform: Windows {system_info.get('platform_info', {}).get('system', 'Unknown')}")
    report.append("")
    
    # System Information
    cpu_info = system_info.get('cpu_info', {})
    memory_info = system_info.get('memory_info', {})
    
    report.append("SYSTEM CONFIGURATION")
    report.append("-" * 40)
    report.append(f"Physical CPU Cores: {cpu_info.get('physical_cores', 'N/A')}")
    report.append(f"Logical CPU Cores: {cpu_info.get('logical_cores', 'N/A')}")
    report.append(f"Total Memory: {memory_info.get('total_gb', 'N/A'):.2f} GB")
    report.append("")
    
    # Performance Analysis
    report.append("PERFORMANCE ANALYSIS")
    report.append("-" * 40)
    
    benchmarks = df['benchmark'].unique()
    
    for benchmark in benchmarks:
        benchmark_data = df[df['benchmark'] == benchmark]
        
        report.append(f"\n{benchmark.replace('_', ' ').title()} Benchmark:")
        
        single_thread = benchmark_data[benchmark_data['threads'] == 1]
        if not single_thread.empty:
            single_time = single_thread['execution_time'].iloc[0]
            report.append(f"  Single-threaded baseline: {single_time:.4f}s")
        
        multi_thread_data = benchmark_data[benchmark_data['threads'] > 1]
        if not multi_thread_data.empty:
            best_result = multi_thread_data.loc[multi_thread_data['speedup'].idxmax()]
            report.append(f"  Best performance: {best_result['threads']} threads")
            report.append(f"  Best execution time: {best_result['execution_time']:.4f}s")
            report.append(f"  Maximum speedup: {best_result['speedup']:.2f}x")
            report.append(f"  Peak efficiency: {best_result['efficiency']:.1f}%")
    
    # OS Concepts Demonstrated
    report.append("\nOPERATING SYSTEM CONCEPTS DEMONSTRATED")
    report.append("-" * 40)
    report.append("1. Threading and Concurrency:")
    report.append("   - Thread creation and management using ThreadPoolExecutor")
    report.append("   - Parallel task execution across multiple CPU cores")
    report.append("   - Thread synchronization and result aggregation")
    report.append("")
    report.append("2. CPU Scheduling:")
    report.append("   - Operating system thread scheduling across logical cores")
    report.append("   - Load balancing and CPU affinity effects")
    report.append("   - Context switching overhead analysis")
    report.append("")
    report.append("3. Memory Management:")
    report.append("   - Memory allocation patterns in multi-threaded environment")
    report.append("   - Garbage collection impact on performance")
    report.append("   - Memory contention between threads")
    report.append("")
    report.append("4. System Resource Utilization:")
    report.append("   - CPU utilization monitoring and optimization")
    report.append("   - Resource contention and bottleneck identification")
    report.append("   - Performance scaling with thread count")
    
    # Key Insights
    report.append("\nKEY INSIGHTS")
    report.append("-" * 40)
    
    avg_speedup_2_threads = df[df['threads'] == 2]['speedup'].mean()
    avg_speedup_4_threads = df[df['threads'] == 4]['speedup'].mean()
    avg_speedup_8_threads = df[df['threads'] == 8]['speedup'].mean()
    
    report.append(f"Average speedup with 2 threads: {avg_speedup_2_threads:.2f}x")
    report.append(f"Average speedup with 4 threads: {avg_speedup_4_threads:.2f}x")
    report.append(f"Average speedup with 8 threads: {avg_speedup_8_threads:.2f}x")
    report.append("")
    report.append("Threading Effectiveness:")
    if avg_speedup_2_threads > 1.5:
        report.append("- Excellent threading benefits observed")
    elif avg_speedup_2_threads > 1.2:
        report.append("- Good threading benefits observed")
    else:
        report.append("- Limited threading benefits (task overhead or dependencies)")
    
    report.append("")
    report.append("Performance Characteristics:")
    report.append("- CPU-bound tasks show better scaling than memory-bound tasks")
    report.append("- Diminishing returns observed with higher thread counts")
    report.append("- Thread overhead becomes significant beyond optimal thread count")
    
    return "\n".join(report)


def main():
    """Main analysis function."""
    print("📊 PERFORMANCE ANALYSIS AND VISUALIZATION")
    print("=" * 60)
    
    # Load benchmark data
    benchmark_data = load_windows_results()
    if not benchmark_data:
        return
    
    # Convert to DataFrame
    df = create_performance_dataframe(benchmark_data)
    print(f"✅ Processed {len(df)} performance data points")
    
    # Create visualizations
    print("📈 Creating performance visualizations...")
    chart_files = create_performance_visualizations(df)
    print(f"✅ Created {len(chart_files)} visualization files:")
    for chart_file in chart_files:
        print(f"  - {chart_file}")
    
    # Generate analysis report
    print("📝 Generating analysis report...")
    system_info = benchmark_data.get('system_info', {})
    report = generate_analysis_report(df, system_info)
    
    # Save report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"performance_analysis_report_{timestamp}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✅ Analysis report saved to: {report_file}")
    print("\n" + "=" * 60)
    print("ANALYSIS COMPLETED SUCCESSFULLY!")
    print("=" * 60)


if __name__ == "__main__":
    main()
