#!/usr/bin/env python3
"""
Windows Platform Benchmark Script

This script runs comprehensive benchmarks on Windows platform
and saves detailed results for analysis.
"""

import os
import sys
import json
import time
import platform
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from multithreaded_calculator.calculator.threading_manager import ThreadingManager
from multithreaded_calculator.calculator.performance_monitor import PerformanceMonitor


def create_results_directory():
    """Create results directory for Windows."""
    results_dir = os.path.join("multithreaded_calculator", "results", "windows_results")
    os.makedirs(results_dir, exist_ok=True)
    return results_dir


def run_comprehensive_benchmark():
    """Run comprehensive benchmark suite for Windows."""
    print("🚀 WINDOWS PLATFORM COMPREHENSIVE BENCHMARK")
    print("=" * 60)
    
    # System information
    monitor = PerformanceMonitor()
    system_info = monitor.get_system_info()
    
    print("💻 SYSTEM INFORMATION")
    print("-" * 40)
    print(f"OS: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.architecture()[0]}")
    print(f"Processor: {platform.processor()}")
    print(f"Physical Cores: {system_info['cpu_info']['physical_cores']}")
    print(f"Logical Cores: {system_info['cpu_info']['logical_cores']}")
    print(f"Total Memory: {system_info['memory_info']['total_gb']:.2f} GB")
    print(f"Python Version: {sys.version}")
    print()
    
    # Initialize threading manager
    manager = ThreadingManager(max_workers=8)
    
    # Test configurations
    thread_counts = [1, 2, 4, 8]
    
    # Results storage
    benchmark_results = {
        "timestamp": datetime.now().isoformat(),
        "platform": "Windows",
        "system_info": system_info,
        "benchmarks": {}
    }
    
    # 1. Prime Search Benchmark
    print("🔍 PRIME SEARCH BENCHMARK")
    print("-" * 40)
    
    prime_start, prime_end = 1, 50000
    print(f"Range: {prime_start} to {prime_end}")
    
    prime_results = manager.benchmark_performance(
        "prime_search", thread_counts, prime_start, prime_end
    )
    
    # Display prime search results
    single_time = prime_results["single_threaded"][0]["execution_time"]
    prime_count = len(prime_results["single_threaded"][0]["result"])
    
    print(f"Primes found: {prime_count}")
    print(f"Single-threaded: {single_time:.4f}s")
    
    for result in prime_results["multi_threaded"]:
        threads = result["thread_count"]
        time_taken = result["execution_time"]
        speedup = single_time / time_taken if time_taken > 0 else 0
        efficiency = speedup / threads * 100
        print(f"{threads:2d} threads: {time_taken:.4f}s "
              f"(speedup: {speedup:.2f}x, efficiency: {efficiency:.1f}%)")
    
    benchmark_results["benchmarks"]["prime_search"] = prime_results
    print()
    
    # 2. CPU Intensive Benchmark
    print("⚡ CPU INTENSIVE BENCHMARK")
    print("-" * 40)
    
    cpu_iterations = 500000
    print(f"Iterations: {cpu_iterations:,}")
    
    cpu_results = manager.benchmark_performance(
        "cpu_intensive", thread_counts, cpu_iterations
    )
    
    # Display CPU intensive results
    single_time = cpu_results["single_threaded"][0]["execution_time"]
    
    print(f"Single-threaded: {single_time:.4f}s")
    
    for result in cpu_results["multi_threaded"]:
        threads = result["thread_count"]
        time_taken = result["execution_time"]
        speedup = single_time / time_taken if time_taken > 0 else 0
        efficiency = speedup / threads * 100
        print(f"{threads:2d} threads: {time_taken:.4f}s "
              f"(speedup: {speedup:.2f}x, efficiency: {efficiency:.1f}%)")
    
    benchmark_results["benchmarks"]["cpu_intensive"] = cpu_results
    print()
    
    # 3. Memory Intensive Benchmark
    print("💾 MEMORY INTENSIVE BENCHMARK")
    print("-" * 40)
    
    memory_size = 30  # MB
    memory_thread_counts = [1, 2, 4]  # Fewer threads for memory test
    print(f"Memory size: {memory_size} MB")
    
    memory_results = manager.benchmark_performance(
        "memory_intensive", memory_thread_counts, memory_size
    )
    
    # Display memory intensive results
    single_time = memory_results["single_threaded"][0]["execution_time"]
    
    print(f"Single-threaded: {single_time:.4f}s")
    
    for result in memory_results["multi_threaded"]:
        threads = result["thread_count"]
        time_taken = result["execution_time"]
        speedup = single_time / time_taken if time_taken > 0 else 0
        efficiency = speedup / threads * 100
        print(f"{threads:2d} threads: {time_taken:.4f}s "
              f"(speedup: {speedup:.2f}x, efficiency: {efficiency:.1f}%)")
    
    benchmark_results["benchmarks"]["memory_intensive"] = memory_results
    print()
    
    # Save results
    results_dir = create_results_directory()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = os.path.join(results_dir, f"windows_benchmark_{timestamp}.json")
    
    with open(results_file, 'w') as f:
        json.dump(benchmark_results, f, indent=2, default=str)
    
    print("📊 BENCHMARK SUMMARY")
    print("-" * 40)
    print(f"Results saved to: {results_file}")
    print(f"Total benchmarks: {len(benchmark_results['benchmarks'])}")
    print(f"System: {platform.system()} {platform.release()}")
    print(f"CPU Cores: {system_info['cpu_info']['logical_cores']}")
    print()
    
    # Performance insights
    print("🔍 KEY INSIGHTS")
    print("-" * 40)
    
    # Prime search analysis
    prime_best = min(prime_results["multi_threaded"], key=lambda x: x["execution_time"])
    prime_speedup = single_time / prime_best["execution_time"]
    print(f"Prime Search: Best speedup {prime_speedup:.2f}x with {prime_best['thread_count']} threads")
    
    # CPU intensive analysis
    cpu_single_time = cpu_results["single_threaded"][0]["execution_time"]
    cpu_best = min(cpu_results["multi_threaded"], key=lambda x: x["execution_time"])
    cpu_speedup = cpu_single_time / cpu_best["execution_time"]
    print(f"CPU Intensive: Best speedup {cpu_speedup:.2f}x with {cpu_best['thread_count']} threads")
    
    # Memory intensive analysis
    mem_single_time = memory_results["single_threaded"][0]["execution_time"]
    mem_best = min(memory_results["multi_threaded"], key=lambda x: x["execution_time"])
    mem_speedup = mem_single_time / mem_best["execution_time"]
    print(f"Memory Intensive: Best speedup {mem_speedup:.2f}x with {mem_best['thread_count']} threads")
    
    print("\n✅ WINDOWS BENCHMARK COMPLETED SUCCESSFULLY!")
    
    return benchmark_results


def main():
    """Main function."""
    try:
        results = run_comprehensive_benchmark()
        return results
    except KeyboardInterrupt:
        print("\n⚠️  Benchmark interrupted by user")
        return None
    except Exception as e:
        print(f"\n❌ Error during benchmark: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
